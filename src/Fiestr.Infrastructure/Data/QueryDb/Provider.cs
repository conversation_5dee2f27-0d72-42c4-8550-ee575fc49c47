namespace Fiestr.Infrastructure.Data.QueryDb;

public partial class Provider
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string PhoneNumber { get; set; } = null!;

    public string Address1 { get; set; } = null!;

    public DateTime CreatedAt { get; set; }

    public string? LogoThumbnailId { get; set; }

    public string? LogoImageId { get; set; }

    public virtual Image? LogoImage { get; set; }

    public virtual Image? LogoThumbnail { get; set; }

    public virtual ICollection<Product> Products { get; set; } = new List<Product>();

    public virtual ICollection<ProviderImage> ProviderImages { get; set; } = new List<ProviderImage>();

    public virtual ICollection<ProviderReview> ProviderReviews { get; set; } = new List<ProviderReview>();

    public virtual ICollection<Tag> Tags { get; set; } = new List<Tag>();

    public virtual ICollection<User> Users { get; set; } = new List<User>();
}
